using System.Collections;
using UnityEngine;
using UnityEngine.InputSystem;

public class JrsOrbitCamera : MonoBehaviour
{
    public Transform target;
    public float rotationSpeed = 5.0f;
    public float rotationSmoothTime = 0.1f;
    public float zoomSpeed = 10.0f;
    public float minZoom = 5.0f;
    public float maxZoom = 15.0f;
    public float zoomTime = 0.5f;
    public float distance;
    [SerializeField] private float middleAreaPercentage = 0.3f; // Adjust this value in the Inspector

    private float yaw = 0.0f;
    private float pitch = 45.0f; // Start from above the vehicle
    private float rotationVelocityX;
    private float rotationVelocityY;
    private Coroutine zoomCoroutine;

    // New Input System variables
    private VehicleInputActions inputActions;
    private Vector2 mouseRotationInput;
    private float mouseZoomInput;
    private bool isMousePressed;

    void Start()
    {
        // If distance is not set in the inspector, use the initial distance to the target
        if (distance == 0)
        {
            distance = Vector3.Distance(transform.position, target.position);
        }

        // Initialize New Input System
        inputActions = new VehicleInputActions();
        inputActions.Vehicle.OrbitCameraRotate.performed += OnOrbitCameraRotate;
        inputActions.Vehicle.OrbitCameraRotate.canceled += OnOrbitCameraRotate;
        inputActions.Vehicle.OrbitCameraZoom.performed += OnOrbitCameraZoom;
        inputActions.Vehicle.OrbitCameraZoom.canceled += OnOrbitCameraZoom;
        inputActions.Enable();
    }

    void OnDestroy()
    {
        if (inputActions != null)
        {
            inputActions.Vehicle.OrbitCameraRotate.performed -= OnOrbitCameraRotate;
            inputActions.Vehicle.OrbitCameraRotate.canceled -= OnOrbitCameraRotate;
            inputActions.Vehicle.OrbitCameraZoom.performed -= OnOrbitCameraZoom;
            inputActions.Vehicle.OrbitCameraZoom.canceled -= OnOrbitCameraZoom;
            inputActions.Dispose();
        }
    }

    private void OnOrbitCameraRotate(InputAction.CallbackContext context)
    {
        mouseRotationInput = context.ReadValue<Vector2>();
    }

    private void OnOrbitCameraZoom(InputAction.CallbackContext context)
    {
        mouseZoomInput = context.ReadValue<float>();
    }

    void Update()
    {
        // Define the middle area based on a percentage of the screen size
        float middleAreaWidth = Screen.width * middleAreaPercentage;
        float middleAreaHeight = Screen.height * middleAreaPercentage;
        float middleAreaX = (Screen.width - middleAreaWidth) / 2;
        float middleAreaY = (Screen.height - middleAreaHeight) / 2;
        Rect middleArea = new Rect(middleAreaX, middleAreaY, middleAreaWidth, middleAreaHeight);

        // Check if the mouse position is within the middle area and left mouse button is pressed
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        bool isLeftMousePressed = Mouse.current.leftButton.isPressed;

        if (middleArea.Contains(mousePosition) && isLeftMousePressed)
        {
            // Use the mouse rotation input from the New Input System
            yaw += rotationSpeed * mouseRotationInput.x * Time.deltaTime;
            pitch -= rotationSpeed * mouseRotationInput.y * Time.deltaTime;
            pitch = Mathf.Clamp(pitch, 5f, 90f); // Limit the pitch angle between -90 and 90 degrees
        }

        if (Input.touchCount == 2)
        {
            Touch touchZero = Input.GetTouch(0);
            Touch touchOne = Input.GetTouch(1);

            // Check if both touches are within the middle area
            if (middleArea.Contains(touchZero.position) && middleArea.Contains(touchOne.position))
            {
                if (touchZero.phase == TouchPhase.Moved && touchOne.phase == TouchPhase.Moved)
                {
                    Vector2 touchZeroPrevPos = touchZero.position - touchZero.deltaPosition;
                    Vector2 touchOnePrevPos = touchOne.position - touchOne.deltaPosition;

                    float prevTouchDeltaMag = (touchZeroPrevPos - touchOnePrevPos).magnitude;
                    float touchDeltaMag = (touchZero.position - touchOne.position).magnitude;

                    float deltaMagnitudeDiff = touchDeltaMag - prevTouchDeltaMag;

                    float scrollInput = deltaMagnitudeDiff * zoomSpeed * Time.deltaTime;

                    if (scrollInput != 0.0f)
                    {
                        float targetZoom = Mathf.Clamp(distance + scrollInput, minZoom, maxZoom);
                        if (zoomCoroutine != null)
                        {
                            StopCoroutine(zoomCoroutine);
                        }
                        zoomCoroutine = StartCoroutine(ZoomToTarget(targetZoom));
                    }

                    // Only perform orbiting if both touches are within the middle area
                    if (middleArea.Contains(touchZero.position) && middleArea.Contains(touchOne.position))
                    {
                        yaw += rotationSpeed * (touchZero.deltaPosition.x + touchOne.deltaPosition.x) / 2;
                        pitch -= rotationSpeed * (touchZero.deltaPosition.y + touchOne.deltaPosition.y) / 2;
                        pitch = Mathf.Clamp(pitch, 5f, 90f); // Limit the pitch angle between -90 and 90 degrees
                    }
                }
            }
        }
        else
        {
            // Zoom in and out with mouse scroll wheel using New Input System
            if (mouseZoomInput != 0.0f)
            {
                float targetZoom = Mathf.Clamp(distance - mouseZoomInput * zoomSpeed, minZoom, maxZoom);
                if (zoomCoroutine != null)
                {
                    StopCoroutine(zoomCoroutine);
                }
                zoomCoroutine = StartCoroutine(ZoomToTarget(targetZoom));
                mouseZoomInput = 0.0f; // Reset the zoom input after processing
            }
        }

        // Smooth the rotation
        float smoothYaw = Mathf.SmoothDampAngle(transform.eulerAngles.y, yaw, ref rotationVelocityX, rotationSmoothTime);
        float smoothPitch = Mathf.SmoothDampAngle(transform.eulerAngles.x, pitch, ref rotationVelocityY, rotationSmoothTime);

        Quaternion rotation = Quaternion.Euler(smoothPitch, smoothYaw, 0.0f);
        transform.position = target.position - rotation * new Vector3(0, 0, distance);

        transform.LookAt(target);
    }

    IEnumerator ZoomToTarget(float targetZoom)
    {
        float startZoom = distance;
        float startTime = Time.time;
        while (Time.time < startTime + zoomTime)
        {
            distance = Mathf.Lerp(startZoom, targetZoom, (Time.time - startTime) / zoomTime);
            yield return null;
        }
        distance = targetZoom;
    }
}
