using System.Collections;
using UnityEngine;
using UnityEngine.InputSystem;

public class JrsOrbitCamera : MonoBehaviour
{
    public Transform target;
    public float rotationSpeed = 5.0f;
    public float rotationSmoothTime = 0.1f;
    public float zoomSpeed = 10.0f;
    [Header("Advanced Zoom Settings (Optional)")]
    public float mouseZoomMultiplier = 1.0f; // Additional multiplier for mouse scroll
    public float gamepadZoomMultiplier = 1.0f; // Additional multiplier for gamepad
    public float minZoom = 5.0f;
    public float maxZoom = 15.0f;
    public float zoomTime = 0.5f;
    public float distance;
    [SerializeField] private float middleAreaPercentage = 0.3f; // Adjust this value in the Inspector

    private float yaw = 0.0f;
    private float pitch = 45.0f; // Start from above the vehicle
    private float rotationVelocityX;
    private float rotationVelocityY;
    private Coroutine zoomCoroutine;

    // New Input System variables
    private VehicleInputActions inputActions;
    private Vector2 rotationInput; // Renamed to be more generic (handles both mouse and gamepad)
    private float zoomInput; // Renamed to be more generic
    private bool isMousePressed;
    private bool zoomInputProcessed = false; // Flag to prevent multiple processing of same input

    void Start()
    {
        // If distance is not set in the inspector, use the initial distance to the target
        if (distance == 0)
        {
            distance = Vector3.Distance(transform.position, target.position);
        }

        // Initialize New Input System
        inputActions = new VehicleInputActions();
        inputActions.Vehicle.OrbitCameraRotate.performed += OnOrbitCameraRotate;
        inputActions.Vehicle.OrbitCameraRotate.canceled += OnOrbitCameraRotate;
        inputActions.Vehicle.OrbitCameraZoom.performed += OnOrbitCameraZoom;
        inputActions.Vehicle.OrbitCameraZoom.canceled += OnOrbitCameraZoom;
        inputActions.Enable();

        Debug.Log("JrsOrbitCamera: Input actions initialized and enabled");
        Debug.Log($"OrbitCameraRotate action enabled: {inputActions.Vehicle.OrbitCameraRotate.enabled}");
        Debug.Log($"OrbitCameraZoom action enabled: {inputActions.Vehicle.OrbitCameraZoom.enabled}");

        // Debug connected devices
        Debug.Log($"Mouse connected: {Mouse.current != null}");
        Debug.Log($"Gamepad connected: {Gamepad.current != null}");
        if (Gamepad.current != null)
        {
            Debug.Log($"Gamepad name: {Gamepad.current.name}");
            Debug.Log($"D-pad up: {Gamepad.current.dpad.up.isPressed}");
            Debug.Log($"D-pad down: {Gamepad.current.dpad.down.isPressed}");
        }

        // Debug input action bindings
        var zoomAction = inputActions.Vehicle.OrbitCameraZoom;
        Debug.Log($"OrbitCameraZoom bindings count: {zoomAction.bindings.Count}");
        for (int i = 0; i < zoomAction.bindings.Count; i++)
        {
            Debug.Log($"Binding {i}: {zoomAction.bindings[i].path} - {zoomAction.bindings[i].name}");
        }
    }

    void OnDestroy()
    {
        if (inputActions != null)
        {
            inputActions.Vehicle.OrbitCameraRotate.performed -= OnOrbitCameraRotate;
            inputActions.Vehicle.OrbitCameraRotate.canceled -= OnOrbitCameraRotate;
            inputActions.Vehicle.OrbitCameraZoom.performed -= OnOrbitCameraZoom;
            inputActions.Vehicle.OrbitCameraZoom.canceled -= OnOrbitCameraZoom;
            inputActions.Dispose();
        }
    }

    private void OnOrbitCameraRotate(InputAction.CallbackContext context)
    {
        rotationInput = context.ReadValue<Vector2>();
        // Debug logging to verify input is being received
        if (rotationInput.magnitude > 0.1f)
        {
            Debug.Log($"Orbit Camera Rotation Input: {rotationInput} from {context.control.device.name}");
        }
    }

    private void OnOrbitCameraZoom(InputAction.CallbackContext context)
    {
        if (context.performed)
        {
            zoomInput = context.ReadValue<float>();
            zoomInputProcessed = false; // Reset flag when new input is received
            Debug.Log($"Zoom Input: {zoomInput} from {context.control.device.name}");
        }
        else if (context.started)
        {
            zoomInput = context.ReadValue<float>();
            zoomInputProcessed = false; // Reset flag when new input is received
        }
        else if (context.canceled)
        {
            zoomInput = 0f;
            zoomInputProcessed = true; // Mark as processed when canceled
        }
    }

    void Update()
    {
        // Debug gamepad D-pad input directly (can be removed after testing)
        if (Gamepad.current != null)
        {
            if (Gamepad.current.dpad.up.wasPressedThisFrame)
            {
                Debug.Log("D-pad UP pressed directly!");
            }
            if (Gamepad.current.dpad.down.wasPressedThisFrame)
            {
                Debug.Log("D-pad DOWN pressed directly!");
            }
        }

        // Define the middle area based on a percentage of the screen size
        float middleAreaWidth = Screen.width * middleAreaPercentage;
        float middleAreaHeight = Screen.height * middleAreaPercentage;
        float middleAreaX = (Screen.width - middleAreaWidth) / 2;
        float middleAreaY = (Screen.height - middleAreaHeight) / 2;
        Rect middleArea = new Rect(middleAreaX, middleAreaY, middleAreaWidth, middleAreaHeight);

        // Handle rotation input from both mouse and gamepad
        bool shouldProcessRotation = false;
        Vector2 currentRotationInput = Vector2.zero;

        // Check for gamepad input (always allow gamepad rotation)
        if (Gamepad.current != null && rotationInput.magnitude > 0.1f)
        {
            shouldProcessRotation = true;
            currentRotationInput = rotationInput;
            Debug.Log($"Gamepad rotation input: {rotationInput}");
        }
        // Check for mouse input (only when mouse is in middle area and left button is pressed)
        else if (Mouse.current != null)
        {
            Vector2 mousePosition = Mouse.current.position.ReadValue();
            bool isLeftMousePressed = Mouse.current.leftButton.isPressed;

            if (middleArea.Contains(mousePosition) && isLeftMousePressed && rotationInput.magnitude > 0.1f)
            {
                shouldProcessRotation = true;
                currentRotationInput = rotationInput;
                Debug.Log($"Mouse rotation input: {rotationInput}");
            }
        }

        if (shouldProcessRotation)
        {
            // Apply rotation input with different scaling for mouse vs gamepad
            float rotationMultiplier = Gamepad.current != null ? 50f : 1f; // Gamepad needs higher multiplier
            yaw += rotationSpeed * currentRotationInput.x * rotationMultiplier * Time.deltaTime;
            pitch -= rotationSpeed * currentRotationInput.y * rotationMultiplier * Time.deltaTime;
            pitch = Mathf.Clamp(pitch, 5f, 90f); // Limit the pitch angle between -90 and 90 degrees
        }

        if (Input.touchCount == 2)
        {
            Touch touchZero = Input.GetTouch(0);
            Touch touchOne = Input.GetTouch(1);

            // Check if both touches are within the middle area
            if (middleArea.Contains(touchZero.position) && middleArea.Contains(touchOne.position))
            {
                if (touchZero.phase == UnityEngine.TouchPhase.Moved && touchOne.phase == UnityEngine.TouchPhase.Moved)
                {
                    Vector2 touchZeroPrevPos = touchZero.position - touchZero.deltaPosition;
                    Vector2 touchOnePrevPos = touchOne.position - touchOne.deltaPosition;

                    float prevTouchDeltaMag = (touchZeroPrevPos - touchOnePrevPos).magnitude;
                    float touchDeltaMag = (touchZero.position - touchOne.position).magnitude;

                    float deltaMagnitudeDiff = touchDeltaMag - prevTouchDeltaMag;

                    float scrollInput = deltaMagnitudeDiff * zoomSpeed * Time.deltaTime;

                    if (scrollInput != 0.0f)
                    {
                        float targetZoom = Mathf.Clamp(distance + scrollInput, minZoom, maxZoom);
                        if (zoomCoroutine != null)
                        {
                            StopCoroutine(zoomCoroutine);
                        }
                        zoomCoroutine = StartCoroutine(ZoomToTarget(targetZoom));
                    }

                    // Only perform orbiting if both touches are within the middle area
                    if (middleArea.Contains(touchZero.position) && middleArea.Contains(touchOne.position))
                    {
                        yaw += rotationSpeed * (touchZero.deltaPosition.x + touchOne.deltaPosition.x) / 2;
                        pitch -= rotationSpeed * (touchZero.deltaPosition.y + touchOne.deltaPosition.y) / 2;
                        pitch = Mathf.Clamp(pitch, 5f, 90f); // Limit the pitch angle between -90 and 90 degrees
                    }
                }
            }
        }

        // Handle zoom input from both mouse scroll wheel and gamepad (outside of touch logic)
        if (Mathf.Abs(zoomInput) > 0.01f && !zoomInputProcessed)
        {
            // Normalize input values for consistent zoom speed across devices
            float normalizedZoomInput;
            float finalZoomSpeed;

            // Check input source and normalize accordingly
            if (Mouse.current != null && Mathf.Abs(Mouse.current.scroll.ReadValue().y) > 0.01f)
            {
                // Mouse scroll wheel gives large values (like 120), normalize to -1 to 1 range
                normalizedZoomInput = Mathf.Clamp(zoomInput / 120f, -1f, 1f);
                finalZoomSpeed = zoomSpeed * mouseZoomMultiplier;
                Debug.Log($"Mouse scroll - Raw: {zoomInput}, Normalized: {normalizedZoomInput}");
            }
            else if (Gamepad.current != null)
            {
                // Gamepad D-pad already gives normalized values (-1, 1)
                normalizedZoomInput = zoomInput;
                finalZoomSpeed = zoomSpeed * gamepadZoomMultiplier;
                Debug.Log($"Gamepad D-pad - Raw: {zoomInput}, Normalized: {normalizedZoomInput}");
            }
            else
            {
                // Fallback normalization
                normalizedZoomInput = Mathf.Clamp(zoomInput, -1f, 1f);
                finalZoomSpeed = zoomSpeed;
            }

            float targetZoom = Mathf.Clamp(distance - normalizedZoomInput * finalZoomSpeed, minZoom, maxZoom);

            Debug.Log($"Zoom Input: {zoomInput}, Normalized: {normalizedZoomInput}, Target Zoom: {targetZoom}, Current Distance: {distance}");

            // Apply smooth zoom for both mouse and gamepad
            if (zoomCoroutine != null)
            {
                StopCoroutine(zoomCoroutine);
            }
            zoomCoroutine = StartCoroutine(ZoomToTarget(targetZoom));

            // Identify input source for debugging
            if (Mouse.current != null && Mathf.Abs(Mouse.current.scroll.ReadValue().y) > 0.01f)
            {
                Debug.Log("Started smooth mouse scroll zoom");
            }
            else if (Gamepad.current != null)
            {
                Debug.Log("Started smooth gamepad zoom");
            }

            // Mark input as processed to prevent multiple coroutine starts
            zoomInputProcessed = true;
        }

        // Smooth the rotation
        float smoothYaw = Mathf.SmoothDampAngle(transform.eulerAngles.y, yaw, ref rotationVelocityX, rotationSmoothTime);
        float smoothPitch = Mathf.SmoothDampAngle(transform.eulerAngles.x, pitch, ref rotationVelocityY, rotationSmoothTime);

        Quaternion rotation = Quaternion.Euler(smoothPitch, smoothYaw, 0.0f);
        transform.position = target.position - rotation * new Vector3(0, 0, distance);

        transform.LookAt(target);
    }

    IEnumerator ZoomToTarget(float targetZoom)
    {
        float startZoom = distance;
        float startTime = Time.time;
        while (Time.time < startTime + zoomTime)
        {
            distance = Mathf.Lerp(startZoom, targetZoom, (Time.time - startTime) / zoomTime);
            yield return null;
        }
        distance = targetZoom;
    }
}
